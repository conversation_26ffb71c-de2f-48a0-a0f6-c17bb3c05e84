import mongoose from 'mongoose';
import Student from '../models/Student.js';
import { StudentCurriculumModel } from '../models/StudentKnowledgeGraphModel.js';
import TestHistory from '../models/TestHistory.js';
import AegisGrader from '../models/AegisGrader.js';
import ChatConversation from '../models/ChatConversation.js';
import { createKnowledgeGraphModel } from '../models/knowledgeGraphModel.js';
import { createQuestionModel } from '../models/Question.js';
import AIQuestion from '../models/AIQuestions.js';
import QuestionTemp from '../models/QuestionTemp.js';
import privacySecurityService from './privacySecurityService.js';

/**
 * Query Execution Engine
 * Securely executes MongoDB queries with privacy controls and data sanitization
 */
class QueryExecutionEngine {
  constructor() {
    this.modelMap = this.initializeModelMap();
    this.privacyFilters = this.initializePrivacyFilters();
    this.accessControlMatrix = this.initializeAccessControlMatrix();
  }

  /**
   * Execute a batch of MongoDB queries with security and privacy controls
   * @param {Array} queries - Array of query objects from textToQueryService
   * @param {string} userType - 'student' or 'teacher'
   * @param {string} userId - User ID for access control
   * @param {string} subject - Subject context
   * @returns {Promise<Object>} Execution results with sanitized data
   */
  async executeQueries(queries, userType, userId, subject) {
    try {
      console.log(`[QUERY-EXECUTION] Executing ${queries.length} queries for ${userType}: ${userId}`);
      
      const results = [];
      const executionMetadata = {
        startTime: Date.now(),
        queriesExecuted: 0,
        queriesSuccessful: 0,
        totalDocuments: 0,
        collectionsAccessed: []
      };

      for (const query of queries) {
        try {
          // Step 1: Validate and sanitize query with privacy controls
          const secureQuery = privacySecurityService.validateAndSanitizeQuery(
            query,
            userType,
            userId,
            query.collection
          );

          // Step 2: Apply additional user-specific filters
          const enhancedQuery = this.applyUserFilters(secureQuery, userType, userId, subject);

          // Step 3: Execute the query
          const queryResult = await this.executeSecureQuery(enhancedQuery);

          // Step 4: Sanitize results with privacy service
          const sanitizedResult = privacySecurityService.sanitizeQueryResults(
            queryResult,
            query.collection,
            userType
          );

          results.push({
            collection: query.collection,
            operation: query.operation,
            success: true,
            data: sanitizedResult,
            documentCount: Array.isArray(sanitizedResult) ? sanitizedResult.length : 1,
            executionTime: Date.now() - executionMetadata.startTime
          });

          executionMetadata.queriesSuccessful++;
          executionMetadata.totalDocuments += Array.isArray(sanitizedResult) ? sanitizedResult.length : 1;
          
          if (!executionMetadata.collectionsAccessed.includes(query.collection)) {
            executionMetadata.collectionsAccessed.push(query.collection);
          }

        } catch (queryError) {
          console.error(`[QUERY-EXECUTION] Query failed for collection ${query.collection}:`, queryError);
          results.push({
            collection: query.collection,
            operation: query.operation,
            success: false,
            error: 'Query execution failed',
            data: null
          });
        }

        executionMetadata.queriesExecuted++;
      }

      executionMetadata.endTime = Date.now();
      executionMetadata.totalExecutionTime = executionMetadata.endTime - executionMetadata.startTime;

      return {
        success: true,
        results,
        metadata: executionMetadata,
        summary: {
          totalQueries: queries.length,
          successfulQueries: executionMetadata.queriesSuccessful,
          totalDocuments: executionMetadata.totalDocuments,
          collectionsAccessed: executionMetadata.collectionsAccessed,
          executionTime: executionMetadata.totalExecutionTime
        }
      };

    } catch (error) {
      console.error('[QUERY-EXECUTION] Batch execution failed:', error);
      return {
        success: false,
        error: error.message,
        results: [],
        fallbackToTraditional: true
      };
    }
  }

  /**
   * Validate user access to specific collections
   */
  validateAccess(collection, userType) {
    const accessMatrix = this.accessControlMatrix[userType];
    if (!accessMatrix) {
      return false;
    }

    return accessMatrix.includes(collection) || accessMatrix.includes('*');
  }

  /**
   * Apply user-specific filters to queries for security
   */
  applyUserFilters(query, userType, userId, subject) {
    const secureQuery = { ...query };

    // Apply user-specific filters based on collection and user type
    switch (query.collection) {
      case 'students':
        if (userType === 'student') {
          // Students can only access their own data
          secureQuery.query = { ...query.query, username: userId };
        } else if (userType === 'teacher') {
          // Teachers can access their class students (implement class filtering)
          secureQuery.query = { ...query.query, ...this.getTeacherClassFilter(userId) };
        }
        break;

      case 'studentKnowledgeGraph':
        if (userType === 'student') {
          secureQuery.query = { ...query.query, studentId: userId };
        } else if (userType === 'teacher') {
          // Teachers access class-wide data
          secureQuery.query = { ...query.query, ...this.getTeacherStudentFilter(userId) };
        }
        break;

      case 'testHistory':
        // Add subject filter if not present
        if (subject && !query.query.subject) {
          secureQuery.query = { ...query.query, subject };
        }
        break;

      case 'aegisGrader':
        if (userType === 'student') {
          // Filter by student's admission number
          secureQuery.query = {
            ...query.query,
            'answerSheets.rollNumber': userId // Assuming userId is admission number
          };
        }
        if (subject && !query.query.subject) {
          secureQuery.query = { ...query.query, subject };
        }
        break;

      case 'chatConversations':
        if (userType === 'teacher') {
          // Block teacher access to chat conversations
          throw new Error('Teachers cannot access student chat conversations');
        }
        secureQuery.query = { ...query.query, userId };
        break;
    }

    // Apply privacy projection - ensure we don't mix inclusion and exclusion
    const privacyProjection = this.privacyFilters[query.collection] || {};
    const queryProjection = query.projection || {};

    // Check if query projection has inclusion fields (value: 1)
    const hasInclusion = Object.values(queryProjection).some(val => val === 1);
    const hasExclusion = Object.values(queryProjection).some(val => val === 0);

    if (hasInclusion && hasExclusion) {
      // If mixed, prioritize exclusion for safety
      secureQuery.projection = {
        ...Object.fromEntries(Object.entries(queryProjection).filter(([k, v]) => v === 0)),
        ...privacyProjection
      };
    } else {
      // Safe to merge
      secureQuery.projection = {
        ...queryProjection,
        ...privacyProjection
      };
    }

    return secureQuery;
  }

  /**
   * Execute a secure query against the appropriate model
   */
  async executeSecureQuery(query) {
    const model = this.getModel(query.collection, query.subject);
    
    if (!model) {
      throw new Error(`Model not found for collection: ${query.collection}`);
    }

    // Set query timeout
    const options = {
      maxTimeMS: query.optimizationHints?.maxTimeMS || 5000
    };

    if (query.operation === 'aggregate') {
      // Execute aggregation pipeline
      return await model.aggregate(query.pipeline).option(options).exec();
    } else {
      // Execute find query
      let queryBuilder = model.find(query.query, query.projection, options);
      
      if (query.sort) {
        queryBuilder = queryBuilder.sort(query.sort);
      }
      
      if (query.limit) {
        queryBuilder = queryBuilder.limit(Math.min(query.limit, 100)); // Safety limit
      }
      
      return await queryBuilder.exec();
    }
  }

  /**
   * Get the appropriate Mongoose model for a collection
   */
  getModel(collection, subject) {
    switch (collection) {
      case 'students':
        return Student;
      case 'studentKnowledgeGraph':
        return StudentCurriculumModel;
      case 'testHistory':
        return TestHistory;
      case 'aegisGrader':
        return AegisGrader;
      case 'chatConversations':
        return ChatConversation;
      case 'curriculumNodes':
        return subject ? createKnowledgeGraphModel(subject) : null;
      case 'questions':
        // Dynamic question model - would need collection name from query
        return createQuestionModel('questionBank_class10_mathematics'); // Default
      case 'aiQuestions':
        return AIQuestion;
      case 'questionTemp':
        return QuestionTemp;
      default:
        return null;
    }
  }

  /**
   * Sanitize query results to remove sensitive data
   */
  sanitizeResults(results, collection, userType) {
    if (!results || results.length === 0) {
      return results;
    }

    const sanitizedResults = Array.isArray(results) ? results : [results];
    
    return sanitizedResults.map(doc => {
      const sanitized = doc.toObject ? doc.toObject() : doc;
      
      // Remove sensitive fields
      this.removeSensitiveFields(sanitized);
      
      // Resolve ObjectIds to meaningful content
      this.resolveObjectIds(sanitized, collection);
      
      // Apply collection-specific sanitization
      this.applyCollectionSanitization(sanitized, collection, userType);
      
      return sanitized;
    });
  }

  /**
   * Remove sensitive fields from results
   */
  removeSensitiveFields(doc) {
    const sensitiveFields = [
      'password', 'securityToken', 'email', 'metadata.ipAddress',
      'metadata.device', 'metadata.browser', '_id', '__v'
    ];

    sensitiveFields.forEach(field => {
      if (field.includes('.')) {
        // Handle nested fields
        const parts = field.split('.');
        let current = doc;
        for (let i = 0; i < parts.length - 1; i++) {
          if (current[parts[i]]) {
            current = current[parts[i]];
          } else {
            break;
          }
        }
        if (current && current[parts[parts.length - 1]]) {
          delete current[parts[parts.length - 1]];
        }
      } else {
        delete doc[field];
      }
    });
  }

  /**
   * Resolve ObjectIds to meaningful content
   */
  resolveObjectIds(doc, collection) {
    // This would be expanded to resolve specific ObjectIds to meaningful names
    // For now, just convert ObjectIds to strings to avoid exposure
    this.convertObjectIdsToStrings(doc);
  }

  /**
   * Convert ObjectIds to strings recursively
   */
  convertObjectIdsToStrings(obj) {
    if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        if (mongoose.Types.ObjectId.isValid(obj[key])) {
          obj[key] = obj[key].toString();
        } else if (typeof obj[key] === 'object') {
          this.convertObjectIdsToStrings(obj[key]);
        }
      });
    }
  }

  /**
   * Apply collection-specific sanitization rules
   */
  applyCollectionSanitization(doc, collection, userType) {
    switch (collection) {
      case 'students':
        if (userType === 'teacher') {
          // Remove personal details for teachers
          delete doc.admissionNumber;
          delete doc.profileImage;
        }
        break;
      
      case 'testHistory':
        // Remove full question content for performance
        if (doc.questions) {
          doc.questionCount = doc.questions.length;
          delete doc.questions;
        }
        break;
    }
  }

  /**
   * Get teacher's class filter (placeholder - implement based on your teacher-class relationship)
   */
  getTeacherClassFilter(teacherId) {
    // This should be implemented based on your teacher-class relationship
    // For now, return empty filter
    return {};
  }

  /**
   * Get teacher's student filter (placeholder)
   */
  getTeacherStudentFilter(teacherId) {
    // This should filter students based on teacher's classes
    return {};
  }

  /**
   * Initialize model mapping
   */
  initializeModelMap() {
    return {
      students: Student,
      studentKnowledgeGraph: StudentCurriculumModel,
      testHistory: TestHistory,
      aegisGrader: AegisGrader,
      chatConversations: ChatConversation
    };
  }

  /**
   * Initialize privacy filters for each collection
   */
  initializePrivacyFilters() {
    return {
      students: {
        password: 0,
        securityToken: 0,
        email: 0,
        'subjects.attemptedTests.metadata': 0
      },
      testHistory: {
        questions: 0 // Exclude for performance
      },
      aegisGrader: {
        'answerSheets.metadata': 0
      },
      chatConversations: {
        'messages.metadata': 0
      }
    };
  }

  /**
   * Initialize access control matrix
   */
  initializeAccessControlMatrix() {
    return {
      student: [
        'students', 'studentKnowledgeGraph', 'testHistory', 
        'aegisGrader', 'chatConversations', 'curriculumNodes', 
        'questions', 'aiQuestions'
      ],
      teacher: [
        'students', 'studentKnowledgeGraph', 'testHistory', 
        'aegisGrader', 'curriculumNodes', 'questions', 'aiQuestions'
        // Note: chatConversations is excluded for teachers
      ]
    };
  }
}

export default new QueryExecutionEngine();
